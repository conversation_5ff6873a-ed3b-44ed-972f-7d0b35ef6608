<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>FODMAP Admin Panel</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f5f5f5; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .auth-section { 
            text-align: center; 
            padding: 40px; 
        }
        .admin-panel { 
            display: none; 
        }
        .form-group { 
            margin: 15px 0; 
        }
        label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: bold; 
        }
        input, textarea, select { 
            width: 100%; 
            padding: 10px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            box-sizing: border-box; 
        }
        textarea { 
            height: 100px; 
            resize: vertical; 
        }
        button { 
            background: #007bff; 
            color: white; 
            padding: 12px 24px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px; 
        }
        button:hover { 
            background: #0056b3; 
        }
        button.secondary { 
            background: #6c757d; 
        }
        button.secondary:hover { 
            background: #545b62; 
        }
        button.danger { 
            background: #dc3545; 
        }
        button.danger:hover { 
            background: #c82333; 
        }
        .error { 
            color: red; 
            margin: 10px 0; 
        }
        .success { 
            color: green; 
            margin: 10px 0; 
        }
        .ingredient-item { 
            display: flex; 
            gap: 10px; 
            margin: 10px 0; 
            align-items: center; 
        }
        .ingredient-item input, .ingredient-item select { 
            flex: 1; 
        }
        .ingredient-item button { 
            flex: none; 
            padding: 8px 12px; 
        }
        .tabs { 
            display: flex; 
            border-bottom: 1px solid #ddd; 
            margin-bottom: 20px; 
        }
        .tab { 
            padding: 10px 20px; 
            cursor: pointer; 
            border-bottom: 2px solid transparent; 
        }
        .tab.active { 
            border-bottom-color: #007bff; 
            color: #007bff; 
        }
        .tab-content { 
            display: none; 
        }
        .tab-content.active { 
            display: block; 
        }
        .two-column { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 20px; 
        }
        @media (max-width: 768px) { 
            .two-column { 
                grid-template-columns: 1fr; 
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Authentication Section -->
        <div id="authSection" class="auth-section">
            <h1>FODMAP Admin Panel</h1>
            <p>Enter admin password to access the recipe management system</p>
            <div class="form-group">
                <input type="password" id="adminPassword" placeholder="Admin Password" />
            </div>
            <button onclick="authenticate()">Login</button>
            <div id="authError" class="error"></div>
        </div>

        <!-- Admin Panel -->
        <div id="adminPanel" class="admin-panel">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h1>FODMAP Recipe Admin</h1>
                <button onclick="logout()" class="secondary">Logout</button>
            </div>

            <!-- Tabs -->
            <div class="tabs">
                <div class="tab active" onclick="showTab('recipes')">Add Recipe</div>
                <div class="tab" onclick="showTab('ingredients')">Add Ingredient</div>
                <div class="tab" onclick="showTab('manage')">Manage Data</div>
            </div>

            <!-- Add Recipe Tab -->
            <div id="recipesTab" class="tab-content active">
                <h2>Add New Recipe</h2>
                <form id="recipeForm">
                    <div class="two-column">
                        <div>
                            <div class="form-group">
                                <label for="recipeTitle">Recipe Title *</label>
                                <input type="text" id="recipeTitle" required />
                            </div>
                            
                            <div class="form-group">
                                <label for="recipeCategory">Category *</label>
                                <select id="recipeCategory" required>
                                    <option value="">Select Category</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="recipePrepTime">Preparation Time (minutes)</label>
                                <input type="number" id="recipePrepTime" min="1" />
                            </div>
                            
                            <div class="form-group">
                                <label for="recipeServingSize">Serving Size</label>
                                <input type="number" id="recipeServingSize" min="1" />
                            </div>
                            
                            <div class="form-group">
                                <label for="recipeImageUrl">Image URL</label>
                                <input type="url" id="recipeImageUrl" />
                            </div>
                        </div>
                        
                        <div>
                            <div class="form-group">
                                <label for="recipeDescription">Description</label>
                                <textarea id="recipeDescription"></textarea>
                            </div>
                        </div>
                    </div>

                    <h3>Recipe Ingredients</h3>
                    <div id="recipeIngredients">
                        <!-- Ingredients will be added here -->
                    </div>
                    <button type="button" onclick="addIngredientToRecipe()">Add Ingredient</button>

                    <div style="margin-top: 20px;">
                        <button type="submit">Create Recipe</button>
                        <button type="button" onclick="clearRecipeForm()" class="secondary">Clear Form</button>
                    </div>
                </form>
                <div id="recipeMessage"></div>
            </div>

            <!-- Add Ingredient Tab -->
            <div id="ingredientsTab" class="tab-content">
                <h2>Add New Ingredient</h2>
                <form id="ingredientForm">
                    <div class="form-group">
                        <label for="ingredientName">Ingredient Name *</label>
                        <input type="text" id="ingredientName" required />
                    </div>
                    
                    <div class="form-group">
                        <label for="ingredientUnit">Quantity Unit</label>
                        <input type="text" id="ingredientUnit" placeholder="e.g., g, ml, szt, tbsp" />
                    </div>
                    
                    <div class="form-group">
                        <label for="ingredientFodmap">FODMAP Level *</label>
                        <select id="ingredientFodmap" required>
                            <option value="">Select FODMAP Level</option>
                            <option value="LOW">LOW</option>
                            <option value="MODERATE">MODERATE</option>
                            <option value="HIGH">HIGH</option>
                        </select>
                    </div>
                    
                    <button type="submit">Add Ingredient</button>
                    <button type="button" onclick="clearIngredientForm()" class="secondary">Clear Form</button>
                </form>
                <div id="ingredientMessage"></div>
            </div>

            <!-- Manage Data Tab -->
            <div id="manageTab" class="tab-content">
                <h2>Manage Data</h2>
                <div class="two-column">
                    <div>
                        <h3>Recent Recipes</h3>
                        <div id="recentRecipes">Loading...</div>
                    </div>
                    <div>
                        <h3>All Ingredients</h3>
                        <div id="allIngredients">Loading...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:3000';
        let adminToken = null;
        let categories = [];
        let ingredients = [];

        // Authentication
        async function authenticate() {
            const password = document.getElementById('adminPassword').value;
            const errorDiv = document.getElementById('authError');
            
            try {
                const response = await fetch(`${API_BASE_URL}/admin/auth`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ password })
                });
                
                const data = await response.json();
                
                if (data.error) {
                    errorDiv.textContent = data.message;
                    return;
                }
                
                adminToken = data.data.token;
                document.getElementById('authSection').style.display = 'none';
                document.getElementById('adminPanel').style.display = 'block';
                
                // Load initial data
                await loadCategories();
                await loadIngredients();
                await loadRecentRecipes();
                
            } catch (error) {
                errorDiv.textContent = 'Authentication failed: ' + error.message;
            }
        }

        function logout() {
            adminToken = null;
            document.getElementById('authSection').style.display = 'block';
            document.getElementById('adminPanel').style.display = 'none';
            document.getElementById('adminPassword').value = '';
            document.getElementById('authError').textContent = '';
        }

        // Tab management
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + 'Tab').classList.add('active');
            event.target.classList.add('active');
            
            // Load data for manage tab
            if (tabName === 'manage') {
                loadRecentRecipes();
                loadAllIngredients();
            }
        }

        // Load data functions
        async function loadCategories() {
            try {
                const response = await fetch(`${API_BASE_URL}/categories`);
                const data = await response.json();
                
                if (!data.error) {
                    categories = data.data;
                    const select = document.getElementById('recipeCategory');
                    select.innerHTML = '<option value="">Select Category</option>';
                    
                    categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Failed to load categories:', error);
            }
        }

        async function loadIngredients() {
            try {
                const response = await fetch(`${API_BASE_URL}/ingredients?limit=1000`);
                const data = await response.json();
                
                if (!data.error) {
                    ingredients = data.data;
                }
            } catch (error) {
                console.error('Failed to load ingredients:', error);
            }
        }

        async function loadRecentRecipes() {
            try {
                const response = await fetch(`${API_BASE_URL}/recipes?limit=10`);
                const data = await response.json();

                const container = document.getElementById('recentRecipes');
                if (data.error || data.data.length === 0) {
                    container.innerHTML = '<p>No recipes found</p>';
                    return;
                }

                container.innerHTML = data.data.map(recipe =>
                    `<div style="padding: 10px; border: 1px solid #ddd; margin: 5px 0; border-radius: 4px;">
                        <strong>${recipe.title}</strong><br>
                        <small>Category: ${recipe.category_id} | Prep: ${recipe.preparation_time || 'N/A'} min</small>
                    </div>`
                ).join('');
            } catch (error) {
                document.getElementById('recentRecipes').innerHTML = '<p>Failed to load recipes</p>';
            }
        }

        async function loadAllIngredients() {
            try {
                const response = await fetch(`${API_BASE_URL}/ingredients?limit=100`);
                const data = await response.json();

                const container = document.getElementById('allIngredients');
                if (data.error || data.data.length === 0) {
                    container.innerHTML = '<p>No ingredients found</p>';
                    return;
                }

                container.innerHTML = data.data.map(ingredient =>
                    `<div style="padding: 5px; border-bottom: 1px solid #eee;">
                        <strong>${ingredient.name}</strong> (${ingredient.quantity_unit || 'unit'}) -
                        <span style="color: ${ingredient.fodmap_level === 'LOW' ? 'green' : ingredient.fodmap_level === 'MODERATE' ? 'orange' : 'red'}">
                            ${ingredient.fodmap_level}
                        </span>
                    </div>`
                ).join('');
            } catch (error) {
                document.getElementById('allIngredients').innerHTML = '<p>Failed to load ingredients</p>';
            }
        }

        // Recipe ingredient management
        function addIngredientToRecipe() {
            const container = document.getElementById('recipeIngredients');
            const ingredientDiv = document.createElement('div');
            ingredientDiv.className = 'ingredient-item';

            ingredientDiv.innerHTML = `
                <select class="ingredient-select" onchange="handleIngredientSelect(this)">
                    <option value="">Select Ingredient</option>
                    <option value="new">+ Add New Ingredient</option>
                    ${ingredients.map(ing => `<option value="${ing.id}">${ing.name}</option>`).join('')}
                </select>
                <input type="text" class="ingredient-name" placeholder="New ingredient name" style="display: none;" />
                <select class="ingredient-fodmap" style="display: none;">
                    <option value="LOW">LOW</option>
                    <option value="MODERATE">MODERATE</option>
                    <option value="HIGH">HIGH</option>
                </select>
                <input type="text" class="ingredient-unit" placeholder="Unit (g, ml, szt)" style="display: none;" />
                <input type="number" class="ingredient-quantity" placeholder="Quantity" step="0.1" min="0" />
                <button type="button" onclick="removeIngredientFromRecipe(this)" class="danger">Remove</button>
            `;

            container.appendChild(ingredientDiv);
        }

        function handleIngredientSelect(select) {
            const ingredientDiv = select.parentElement;
            const nameInput = ingredientDiv.querySelector('.ingredient-name');
            const fodmapSelect = ingredientDiv.querySelector('.ingredient-fodmap');
            const unitInput = ingredientDiv.querySelector('.ingredient-unit');

            if (select.value === 'new') {
                nameInput.style.display = 'block';
                fodmapSelect.style.display = 'block';
                unitInput.style.display = 'block';
                nameInput.required = true;
            } else {
                nameInput.style.display = 'none';
                fodmapSelect.style.display = 'none';
                unitInput.style.display = 'none';
                nameInput.required = false;
            }
        }

        function removeIngredientFromRecipe(button) {
            button.parentElement.remove();
        }

        // Form submissions
        document.getElementById('recipeForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const messageDiv = document.getElementById('recipeMessage');
            messageDiv.innerHTML = '<p>Creating recipe...</p>';

            try {
                // Collect recipe data
                const recipeData = {
                    title: document.getElementById('recipeTitle').value,
                    description: document.getElementById('recipeDescription').value,
                    preparation_time: parseInt(document.getElementById('recipePrepTime').value) || null,
                    serving_size: parseInt(document.getElementById('recipeServingSize').value) || null,
                    image_url: document.getElementById('recipeImageUrl').value || null,
                    category_id: parseInt(document.getElementById('recipeCategory').value),
                    created_by: 'admin',
                    ingredients: []
                };

                // Process ingredients
                const ingredientItems = document.querySelectorAll('.ingredient-item');
                for (const item of ingredientItems) {
                    const select = item.querySelector('.ingredient-select');
                    const quantity = parseFloat(item.querySelector('.ingredient-quantity').value);

                    if (!quantity || quantity <= 0) continue;

                    if (select.value === 'new') {
                        // Create new ingredient first
                        const newIngredientData = {
                            name: item.querySelector('.ingredient-name').value,
                            fodmap_level: item.querySelector('.ingredient-fodmap').value,
                            quantity_unit: item.querySelector('.ingredient-unit').value || 'unit'
                        };

                        const ingredientResponse = await fetch(`${API_BASE_URL}/ingredients`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(newIngredientData)
                        });

                        const ingredientResult = await ingredientResponse.json();
                        if (ingredientResult.error) {
                            throw new Error(`Failed to create ingredient: ${ingredientResult.message}`);
                        }

                        recipeData.ingredients.push({
                            ingredient_id: ingredientResult.data.id,
                            quantity: quantity
                        });
                    } else if (select.value) {
                        recipeData.ingredients.push({
                            ingredient_id: parseInt(select.value),
                            quantity: quantity
                        });
                    }
                }

                // Create recipe
                const response = await fetch(`${API_BASE_URL}/recipes`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(recipeData)
                });

                const result = await response.json();

                if (result.error) {
                    messageDiv.innerHTML = `<p class="error">Error: ${result.message}</p>`;
                } else {
                    messageDiv.innerHTML = `<p class="success">Recipe "${result.data.title}" created successfully!</p>`;
                    clearRecipeForm();
                    await loadIngredients(); // Refresh ingredients list
                }

            } catch (error) {
                messageDiv.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        });

        document.getElementById('ingredientForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const messageDiv = document.getElementById('ingredientMessage');
            messageDiv.innerHTML = '<p>Adding ingredient...</p>';

            try {
                const ingredientData = {
                    name: document.getElementById('ingredientName').value,
                    quantity_unit: document.getElementById('ingredientUnit').value || 'unit',
                    fodmap_level: document.getElementById('ingredientFodmap').value
                };

                const response = await fetch(`${API_BASE_URL}/ingredients`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(ingredientData)
                });

                const result = await response.json();

                if (result.error) {
                    messageDiv.innerHTML = `<p class="error">Error: ${result.message}</p>`;
                } else {
                    messageDiv.innerHTML = `<p class="success">Ingredient "${result.data.name}" added successfully!</p>`;
                    clearIngredientForm();
                    await loadIngredients(); // Refresh ingredients list
                }

            } catch (error) {
                messageDiv.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        });

        // Form clearing functions
        function clearRecipeForm() {
            document.getElementById('recipeForm').reset();
            document.getElementById('recipeIngredients').innerHTML = '';
            document.getElementById('recipeMessage').innerHTML = '';
        }

        function clearIngredientForm() {
            document.getElementById('ingredientForm').reset();
            document.getElementById('ingredientMessage').innerHTML = '';
        }

        // Initialize with one ingredient field
        document.addEventListener('DOMContentLoaded', () => {
            // Auto-focus password field
            document.getElementById('adminPassword').focus();

            // Allow Enter key to login
            document.getElementById('adminPassword').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    authenticate();
                }
            });
        });
    </script>
</body>
</html>
